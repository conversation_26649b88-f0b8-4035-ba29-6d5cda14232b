import React from 'react';
import PageLayout from '../../components/layout/PageLayout';

const OurLeadership: React.FC = () => {
  return (
    <PageLayout category="about" hideHero={true} hideBreadcrumbs={true}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem 1rem',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        minHeight: '100vh'
      }}>
        {/* Header Section */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{
            color: '#1a202c',
            fontSize: '3.0rem',
            fontWeight: 800,
            marginBottom: '1rem',
            letterSpacing: '-2px',
            background: 'linear-gradient(135deg, #1a202c 0%, #4a5568 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Management Team
          </h1>
          <div style={{
            width: '80px',
            height: '4px',
            background: 'linear-gradient(90deg, #3182ce, #63b3ed)',
            margin: '0 auto 1.5rem auto',
            borderRadius: '2px'
          }}></div>
          <p style={{
            color: '#4a5568',
            fontSize: '1.2rem',
            maxWidth: '650px',
            margin: '0 auto',
            lineHeight: 1.7,
            fontWeight: 400
          }}>
            Meet our experienced leadership team driving innovation and excellence across all operations.
          </p>
        </div>

        {/* Management Team Image */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: '3rem'
        }}>
          <div style={{
            background: '#ffffff',
            borderRadius: '20px',
            padding: '2rem',
            boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 25px rgba(0,0,0,0.06)',
            border: '1px solid #e2e8f0',
            maxWidth: '100%',
            overflow: 'hidden'
          }}>
            <img
              src="/Management Team_page-0001.jpg"
              alt="Management Team"
              style={{
                width: '100%',
                height: 'auto',
                maxWidth: '1000px',
                borderRadius: '12px',
                display: 'block'
              }}
            />
          </div>
        </div>

        {/* Bottom Section */}
        <div style={{
          textAlign: 'center',
          marginTop: '3rem',
          padding: '2rem',
          background: 'rgba(255, 255, 255, 0.8)',
          borderRadius: '16px',
          border: '1px solid #e2e8f0'
        }}>
          <p style={{
            color: '#4a5568',
            fontSize: '1.1rem',
            fontStyle: 'italic',
            margin: 0
          }}>
            "Leadership is not about being in charge. It's about taking care of those in your charge."
          </p>
        </div>
      </div>
    </PageLayout>
  );
};

export default OurLeadership;