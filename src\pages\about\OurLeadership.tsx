import React from 'react';
import PageLayout from '../../components/layout/PageLayout';

// Team members data - you can add more members here
const teamMembers = [
  {
    name: '<PERSON><PERSON>',
    position: 'Managing Director & CEO',
    image: '/nikh<PERSON>-<PERSON><PERSON><PERSON>.jpg' // Update this path when you upload the image
  },
  // Add more team members here as needed
  // {
  //   name: 'Person Name',
  //   position: 'Position Title',
  //   image: '/person-name.jpg'
  // },
];

const OurLeadership: React.FC = () => {
  return (
    <PageLayout category="about" hideHero={true} hideBreadcrumbs={true}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem 1rem',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        minHeight: '100vh'
      }}>
        {/* Header Section */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{
            color: '#1a202c',
            fontSize: '3.0rem',
            fontWeight: 800,
            marginBottom: '1rem',
            letterSpacing: '-2px',
            background: 'linear-gradient(135deg, #1a202c 0%, #4a5568 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Management Team
          </h1>
          <div style={{
            width: '80px',
            height: '4px',
            background: 'linear-gradient(90deg, #3182ce, #63b3ed)',
            margin: '0 auto 1.5rem auto',
            borderRadius: '2px'
          }}></div>
          <p style={{
            color: '#4a5568',
            fontSize: '1.2rem',
            maxWidth: '650px',
            margin: '0 auto',
            lineHeight: 1.7,
            fontWeight: 400
          }}>
            Meet our experienced leadership team driving innovation and excellence across all operations.
          </p>
        </div>

        {/* Team Members Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          {teamMembers.map((member, index) => (
            <div
              key={index}
              style={{
                background: '#ffffff',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
                border: '1px solid #e2e8f0',
                textAlign: 'center',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseOver={e => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
              }}
              onMouseOut={e => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
              }}
            >
              {/* Profile Image */}
              <div style={{
                width: '150px',
                height: '150px',
                borderRadius: '50%',
                margin: '0 auto 1.5rem auto',
                overflow: 'hidden',
                border: '4px solid #f0f4f8',
                boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                background: 'linear-gradient(135deg, #3182ce, #63b3ed)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}>
                <img
                  src={member.image}
                  alt={member.name}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    zIndex: 2
                  }}
                  onError={(e) => {
                    // Hide image if it fails to load, showing the initials background
                    e.currentTarget.style.display = 'none';
                  }}
                />
                {/* Initials fallback - always present but behind the image */}
                <span style={{
                  color: 'white',
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  zIndex: 1
                }}>
                  {member.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>

              {/* Member Info */}
              <h3 style={{
                color: '#1a202c',
                fontSize: '1.5rem',
                fontWeight: 700,
                margin: '0 0 0.5rem 0',
                letterSpacing: '-0.5px'
              }}>
                {member.name}
              </h3>

              <p style={{
                color: '#3182ce',
                fontSize: '1rem',
                fontWeight: 600,
                margin: '0',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                {member.position}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div style={{
          textAlign: 'center',
          marginTop: '3rem',
          padding: '2rem',
          background: 'rgba(255, 255, 255, 0.8)',
          borderRadius: '16px',
          border: '1px solid #e2e8f0'
        }}>
          <p style={{
            color: '#4a5568',
            fontSize: '1.1rem',
            fontStyle: 'italic',
            margin: 0
          }}>
            "Leadership is not about being in charge. It's about taking care of those in your charge."
          </p>
        </div>
      </div>
    </PageLayout>
  );
};

export default OurLeadership;