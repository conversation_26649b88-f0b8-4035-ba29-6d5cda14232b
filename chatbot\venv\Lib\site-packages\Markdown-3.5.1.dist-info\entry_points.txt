[console_scripts]
markdown_py = markdown.__main__:run

[markdown.extensions]
abbr = markdown.extensions.abbr:AbbrExtension
admonition = markdown.extensions.admonition:AdmonitionExtension
attr_list = markdown.extensions.attr_list:AttrListExtension
codehilite = markdown.extensions.codehilite:CodeHiliteExtension
def_list = markdown.extensions.def_list:DefListExtension
extra = markdown.extensions.extra:ExtraExtension
fenced_code = markdown.extensions.fenced_code:FencedCodeExtension
footnotes = markdown.extensions.footnotes:FootnoteExtension
legacy_attrs = markdown.extensions.legacy_attrs:LegacyAttrExtension
legacy_em = markdown.extensions.legacy_em:LegacyEmExtension
md_in_html = markdown.extensions.md_in_html:MarkdownInHtmlExtension
meta = markdown.extensions.meta:MetaExtension
nl2br = markdown.extensions.nl2br:Nl2BrExtension
sane_lists = markdown.extensions.sane_lists:SaneListExtension
smarty = markdown.extensions.smarty:SmartyExtension
tables = markdown.extensions.tables:TableExtension
toc = markdown.extensions.toc:TocExtension
wikilinks = markdown.extensions.wikilinks:WikiLinkExtension
